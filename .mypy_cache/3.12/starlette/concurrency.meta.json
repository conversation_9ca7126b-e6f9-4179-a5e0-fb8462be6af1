{"data_mtime": 1753170993, "dep_lines": [6, 9, 1, 3, 4, 5, 7, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "anyio.to_thread", "__future__", "functools", "sys", "warnings", "typing", "anyio", "builtins", "_frozen_importlib", "_typeshed", "abc", "anyio._core", "anyio._core._synchronization"], "hash": "3bdaaa7afa02e9be4908198fe552c8e1c0df3cb8", "id": "starlette.concurrency", "ignore_all": true, "interface_hash": "b6b37c490d54e7be3a3ba6706e903d75f82d6512", "mtime": 1753170337, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/.cache/pre-commit/repozd859_xw/py_env-python3.13/lib/python3.13/site-packages/starlette/concurrency.py", "plugin_data": null, "size": 1786, "suppressed": [], "version_id": "1.17.0"}