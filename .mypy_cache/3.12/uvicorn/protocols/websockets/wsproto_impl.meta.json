{"data_mtime": 1753170993, "dep_lines": [27, 6, 14, 25, 26, 35, 1, 3, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 10, 11, 12, 8], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5], "dependencies": ["uvicorn.protocols.utils", "urllib.parse", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "logging", "typing", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.events", "asyncio.locks", "asyncio.mixins", "asyncio.protocols", "asyncio.queues", "asyncio.transports", "types", "urllib", "uvicorn.protocols.http", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.websockets_impl", "uvicorn.protocols.websockets.websockets_sansio_impl"], "hash": "47b930614e7e48f600373b90d8abd5d933065e0b", "id": "uvicorn.protocols.websockets.wsproto_impl", "ignore_all": true, "interface_hash": "9adf193f584ad009d3e9302945fe9f28a43b6bf4", "mtime": 1753170335, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/.cache/pre-commit/repozd859_xw/py_env-python3.13/lib/python3.13/site-packages/uvicorn/protocols/websockets/wsproto_impl.py", "plugin_data": null, "size": 15366, "suppressed": ["wsproto.connection", "wsproto.extensions", "wsproto.utilities", "wsproto"], "version_id": "1.17.0"}