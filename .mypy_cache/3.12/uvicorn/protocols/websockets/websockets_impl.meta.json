{"data_mtime": 1753170993, "dep_lines": [35, 6, 8, 20, 33, 34, 43, 1, 3, 4, 5, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 11, 14, 15, 16, 11, 12, 13, 17, 18, 10], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10, 5, 5, 5, 20, 5, 5, 5, 5, 10], "dependencies": ["uvicorn.protocols.utils", "collections.abc", "urllib.parse", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "http", "logging", "typing", "builtins", "_asyncio", "_collections_abc", "_contextvars", "_frozen_importlib", "abc", "asyncio.events", "asyncio.locks", "asyncio.mixins", "asyncio.protocols", "asyncio.transports", "enum", "types", "urllib", "uvicorn.protocols.http", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.websockets_sansio_impl", "uvicorn.protocols.websockets.wsproto_impl"], "hash": "9cd6bef56f6422a087fd2678a56b05df3d5f1153", "id": "uvicorn.protocols.websockets.websockets_impl", "ignore_all": true, "interface_hash": "39a578c545992a07eccb74dfb36a01f29bf1ad5e", "mtime": 1753170335, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/.cache/pre-commit/repozd859_xw/py_env-python3.13/lib/python3.13/site-packages/uvicorn/protocols/websockets/websockets_impl.py", "plugin_data": null, "size": 15546, "suppressed": ["websockets.legacy.handshake", "websockets.extensions.base", "websockets.extensions.permessage_deflate", "websockets.legacy.server", "websockets.legacy", "websockets.datastructures", "websockets.exceptions", "websockets.server", "websockets.typing", "websockets"], "version_id": "1.17.0"}