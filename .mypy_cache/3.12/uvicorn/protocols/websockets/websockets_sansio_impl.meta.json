{"data_mtime": 1753170993, "dep_lines": [29, 6, 9, 17, 27, 28, 36, 1, 3, 4, 5, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 12, 11, 13, 14, 15], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5], "dependencies": ["uvicorn.protocols.utils", "asyncio.transports", "urllib.parse", "uvicorn._types", "uvicorn.config", "uvicorn.logging", "uvicorn.server", "__future__", "asyncio", "logging", "sys", "http", "typing", "builtins", "_asyncio", "_collections_abc", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "asyncio.locks", "asyncio.mixins", "asyncio.protocols", "asyncio.queues", "enum", "types", "urllib", "uvicorn.protocols.http", "uvicorn.protocols.http.h11_impl", "uvicorn.protocols.http.httptools_impl", "uvicorn.protocols.websockets.websockets_impl", "uvicorn.protocols.websockets.wsproto_impl"], "hash": "38bfb13be7a8bb510f12de0b1317a6af26dfaac7", "id": "uvicorn.protocols.websockets.websockets_sansio_impl", "ignore_all": true, "interface_hash": "06a5f58bda5dd9a0a409dc14f3d60262b936de15", "mtime": 1753170335, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/.cache/pre-commit/repozd859_xw/py_env-python3.13/lib/python3.13/site-packages/uvicorn/protocols/websockets/websockets_sansio_impl.py", "plugin_data": null, "size": 17145, "suppressed": ["websockets.extensions.permessage_deflate", "websockets.exceptions", "websockets.frames", "websockets.http11", "websockets.server"], "version_id": "1.17.0"}