{"data_mtime": 1753170992, "dep_lines": [50, 51, 52, 53, 54, 55, 56, 60, 34, 35, 36, 37, 38, 46, 48, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 25, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic.v1.class_validators", "pydantic.v1.config", "pydantic.v1.error_wrappers", "pydantic.v1.errors", "pydantic.v1.fields", "pydantic.v1.main", "pydantic.v1.utils", "pydantic.v1.typing", "copy", "dataclasses", "sys", "contextlib", "functools", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "types"], "hash": "f9117863bbd534cd907ccbc2981dc79a384b6df4", "id": "pydantic.v1.dataclasses", "ignore_all": true, "interface_hash": "93f1f4d52614b3d2e8d74ce1e4b5a6471d08004c", "mtime": 1753170338, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "/Users/<USER>/.cache/pre-commit/repozd859_xw/py_env-python3.13/lib/python3.13/site-packages/pydantic/v1/dataclasses.py", "plugin_data": null, "size": 18172, "suppressed": [], "version_id": "1.17.0"}