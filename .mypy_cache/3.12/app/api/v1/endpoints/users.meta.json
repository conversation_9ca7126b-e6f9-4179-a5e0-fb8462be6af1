{"data_mtime": **********, "dep_lines": [3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["<PERSON><PERSON><PERSON>", "pydantic", "builtins", "_frozen_importlib", "_typeshed", "abc", "contextlib", "enum", "fastapi.exceptions", "fastapi.params", "fastapi.routing", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette", "starlette.exceptions", "starlette.responses", "starlette.routing", "typing"], "hash": "3fc66ca64443aad7be6bbe0006e68c6f1436dfe2", "id": "app.api.v1.endpoints.users", "ignore_all": false, "interface_hash": "28d720c976e04951a3bdce3d3b04a183de9d7a25", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": false, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "app/api/v1/endpoints/users.py", "plugin_data": null, "size": 1455, "suppressed": [], "version_id": "1.17.0"}