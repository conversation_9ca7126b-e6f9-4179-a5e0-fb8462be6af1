.PHONY: help clean up-dev down-dev up-prod down-prod

help: ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# Cleanup
clean: ## Clean up cache and temporary files
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf dist
	rm -rf build
	rm -rf *.egg-info

# Docker Compose environments
up-dev: ## Start development environment with Docker Compose
	docker compose up --build -d

logs-dev: ## Logs development environment with Docker Compose
	docker compose logs -f

down-dev: ## Stop development environment
	docker compose down

up-prod: ## Start production environment with Docker Compose
	docker compose -f docker-compose.prod.yml up --build -d

logs-prod: ## Logs production environment with Docker Compose
	docker compose -f docker-compose.prod.yml logs -f

down-prod: ## Stop production environment
	docker compose -f docker-compose.prod.yml down
