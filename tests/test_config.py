"""Test configuration module."""

import os
import tempfile
from unittest.mock import patch

import pytest

from app.config import Settings


def test_settings_defaults():
    """Test that settings have correct default values when no env file is used."""
    # Create settings without loading .env file
    settings = Settings(_env_file=None)

    assert settings.app_name == "FastAPI Template"
    assert settings.app_version == "0.1.0"
    assert settings.debug is False
    assert settings.host == "0.0.0.0"
    assert settings.port == 8000
    assert settings.reload is False
    assert settings.cors_origins == ["http://localhost:3000", "http://localhost:8080"]
    assert settings.api_v1_prefix == "/api/v1"
    assert settings.secret_key == "dev-secret-key-change-in-production"
    assert settings.access_token_expire_minutes == 30
    assert settings.postgres_db == "fastapi_db"
    assert settings.postgres_user == "fastapi_user"
    assert settings.postgres_password == "secure_password_change_this"
    assert settings.log_level == "info"


def test_settings_from_env_vars():
    """Test that settings can be loaded from environment variables."""
    env_vars = {
        "APP_NAME": "Test App",
        "APP_VERSION": "1.0.0",
        "DEBUG": "true",
        "HOST": "127.0.0.1",
        "PORT": "9000",
        "SECRET_KEY": "test-secret-key-with-sufficient-length",
        "LOG_LEVEL": "debug",
    }

    with patch.dict(os.environ, env_vars, clear=True):
        settings = Settings(_env_file=None)

        assert settings.app_name == "Test App"
        assert settings.app_version == "1.0.0"
        assert settings.debug is True
        assert settings.host == "127.0.0.1"
        assert settings.port == 9000
        assert settings.secret_key == "test-secret-key-with-sufficient-length"
        assert settings.log_level == "debug"


def test_cors_origins_parsing():
    """Test CORS origins parsing from different formats."""
    # Test JSON string format
    with patch.dict(os.environ, {"CORS_ORIGINS": '["https://example.com", "https://api.example.com"]'}, clear=True):
        settings = Settings(_env_file=None)
        assert settings.cors_origins == ["https://example.com", "https://api.example.com"]

    # Test comma-separated string format
    with patch.dict(os.environ, {"CORS_ORIGINS": "https://example.com,https://api.example.com"}, clear=True):
        settings = Settings(_env_file=None)
        assert settings.cors_origins == ["https://example.com", "https://api.example.com"]


def test_database_url_property():
    """Test database URL construction."""
    settings = Settings(
        postgres_user="testuser",
        postgres_password="testpass",
        postgres_db="testdb"
    )

    expected_url = "postgresql://testuser:testpass@localhost:5432/testdb"
    assert settings.database_url == expected_url


def test_port_validation():
    """Test port number validation."""
    # Valid port
    settings = Settings(port=8080)
    assert settings.port == 8080

    # Invalid port - too low
    with pytest.raises(ValueError):
        Settings(port=0)

    # Invalid port - too high
    with pytest.raises(ValueError):
        Settings(port=70000)


def test_secret_key_validation():
    """Test secret key validation and warning."""
    # Test with default key (should warn)
    with pytest.warns(UserWarning, match="Using default secret key"):
        Settings(secret_key="dev-secret-key-change-in-production")

    # Test with another default key (should warn)
    with pytest.warns(UserWarning, match="Using default secret key"):
        Settings(secret_key="change-this-to-a-secure-random-string")

    # Test with custom key (should not warn)
    settings = Settings(secret_key="my-custom-secret-key-that-is-long-enough")
    assert settings.secret_key == "my-custom-secret-key-that-is-long-enough"


def test_log_level_validation():
    """Test log level validation."""
    # Valid log levels
    for level in ["debug", "info", "warning", "error", "critical"]:
        settings = Settings(log_level=level)
        assert settings.log_level == level

    # Invalid log level
    with pytest.raises(ValueError):
        Settings(log_level="invalid")


def test_settings_from_env_file():
    """Test loading settings from .env file."""
    env_content = """
APP_NAME=File Test App
DEBUG=true
PORT=7000
SECRET_KEY=file-secret-key-with-sufficient-length
CORS_ORIGINS=["https://file.example.com"]
"""

    with tempfile.NamedTemporaryFile(mode='w', suffix='.env', delete=False) as f:
        f.write(env_content)
        f.flush()

        try:
            # Create settings with custom env file
            settings = Settings(_env_file=f.name)

            assert settings.app_name == "File Test App"
            assert settings.debug is True
            assert settings.port == 7000
            assert settings.secret_key == "file-secret-key-with-sufficient-length"
            assert settings.cors_origins == ["https://file.example.com"]
        finally:
            os.unlink(f.name)


def test_extra_fields_ignored():
    """Test that extra fields in environment are ignored."""
    env_vars = {
        "APP_NAME": "Test App",
        "UNKNOWN_FIELD": "should be ignored",
        "ANOTHER_UNKNOWN": "also ignored",
    }

    with patch.dict(os.environ, env_vars, clear=True):
        # Should not raise validation error
        settings = Settings(_env_file=None)
        assert settings.app_name == "Test App"
        # Unknown fields should not be accessible
        assert not hasattr(settings, 'unknown_field')
        assert not hasattr(settings, 'another_unknown')
