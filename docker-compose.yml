services:
  # FastAPI application (development)
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/.venv  # Exclude virtual environment from volume mount
    environment:
      - APP_NAME=${APP_NAME}
      - APP_VERSION=${APP_VERSION}
      - HOST=${HOST}
      - PORT=${PORT}
      - DEBUG=true
      - RELOAD=true
      - CORS_ORIGINS=${CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES}
      - API_V1_PREFIX=${API_V1_PREFIX}
      - LOG_LEVEL=${LOG_LEVEL}
      - POSTGRES_DB=${POSTGRES_DB:-fastapi_db}
      - POSTGRES_USER=${POSTGRES_USER:-fastapi_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    depends_on:
      - db
    networks:
      - app-dev-network
    restart: unless-stopped

  # PostgreSQL database (optional - uncomment if needed)
  db:
    image: postgres:17-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-fastapi_db}
      - POSTGRES_USER=${POSTGRES_USER:-fastapi_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    ports:
      - "15432:5432"
    networks:
      - app-dev-network
    restart: unless-stopped

networks:
  app-dev-network:
    driver: bridge
