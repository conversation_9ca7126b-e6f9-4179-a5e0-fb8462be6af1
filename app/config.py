"""Application configuration using Pydantic Settings."""

from typing import Literal

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


def parse_cors_origins(v: str | list[str]) -> list[str]:
    """Parse CORS origins from string or list."""
    if isinstance(v, str):
        # Handle JSON string format from environment variables
        import json
        try:
            return json.loads(v)
        except json.JSONDecodeError:
            # Handle comma-separated string format
            return [origin.strip() for origin in v.split(',') if origin.strip()]
    return v


class Settings(BaseSettings):
    """Application settings."""

    # Application
    app_name: str = Field(default="FastAPI Template", description="Application name")
    app_version: str = Field(default="0.1.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")

    # Server
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port", ge=1, le=65535)
    reload: bool = Field(default=False, description="Auto-reload on code changes")

    # CORS
    cors_origins_raw: str = Field(
        default='["http://localhost:3000", "http://localhost:8080"]',
        description="CORS allowed origins (JSON array or comma-separated)",
        alias="cors_origins"
    )

    # API
    api_v1_prefix: str = Field(default="/api/v1", description="API v1 prefix")

    # Security
    secret_key: str = Field(
        default="dev-secret-key-change-in-production",
        description="Secret key for JWT tokens and other cryptographic operations",
        min_length=32
    )
    access_token_expire_minutes: int = Field(
        default=30,
        description="Access token expiration time in minutes",
        ge=1
    )

    # Database
    postgres_db: str = Field(
        default="fastapi_db",
        description="PostgreSQL database name"
    )
    postgres_user: str = Field(
        default="fastapi_user",
        description="PostgreSQL username"
    )
    postgres_password: str = Field(
        default="secure_password_change_this",
        description="PostgreSQL password"
    )

    # Logging
    log_level: Literal["debug", "info", "warning", "error", "critical"] = Field(
        default="info",
        description="Logging level"
    )

    # Computed properties
    @property
    def cors_origins(self) -> list[str]:
        """Parse CORS origins from raw string."""
        return parse_cors_origins(self.cors_origins_raw)

    @property
    def database_url(self) -> str:
        """Construct database URL from components."""
        return f"postgresql://{self.postgres_user}:{self.postgres_password}@localhost:5432/{self.postgres_db}"



    @field_validator('secret_key')
    @classmethod
    def validate_secret_key(cls, v):
        """Validate secret key strength."""
        if v == "dev-secret-key-change-in-production" or v == "change-this-to-a-secure-random-string":
            import warnings
            warnings.warn(
                "Using default secret key! Change this in production!",
                UserWarning,
                stacklevel=2
            )
        return v

    # Pydantic v2 configuration
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",  # Ignore extra fields instead of forbidding them
        validate_default=True,
        # Disable automatic JSON parsing for complex types to allow custom parsing
        env_parse_none_str=None,
        # Environment variable prefix (optional)
        # env_prefix="APP_",
    )


# Global settings instance
settings = Settings()
