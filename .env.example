# Production Environment Configuration
APP_NAME=FastAPI Template
APP_VERSION=0.1.0

# Server Configuration
HOST=0.0.0.0
PORT=8000

# CORS Configuration (restrict to your actual domains)
CORS_ORIGINS=["https://yourdomain.com", "https://www.yourdomain.com"]

# API Configuration
API_V1_PREFIX=/api/v1

# Security (MUST be changed for production)
SECRET_KEY=change-this-to-a-secure-random-string
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Credentials
POSTGRES_DB=fastapi_db
POSTGRES_USER=fastapi_user
POSTGRES_PASSWORD=secure_password_change_this

# Logging
LOG_LEVEL=info
