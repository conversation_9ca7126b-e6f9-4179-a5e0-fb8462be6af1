services:
  # FastAPI application (production)
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    environment:
      - APP_NAME=${APP_NAME}
      - APP_VERSION=${APP_VERSION}
      - HOST=${HOST}
      - PORT=${PORT}
      - DEBUG=false
      - RELOAD=false
      - CORS_ORIGINS=${CORS_ORIGINS}
      - SECRET_KEY=${SECRET_KEY}
      - ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES}
      - API_V1_PREFIX=${API_V1_PREFIX}
      - LOG_LEVEL=${LOG_LEVEL}
      - POSTGRES_DB=${POSTGRES_DB:-fastapi_db}
      - POSTGRES_USER=${POSTGRES_USER:-fastapi_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    depends_on:
      - db
    networks:
      - app-prod-network
    restart: unless-stopped

  # PostgreSQL database
  db:
    image: postgres:17-alpine
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-fastapi_db}
      POSTGRES_USER: ${POSTGRES_USER:-fastapi_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
    networks:
      - app-prod-network
    restart: unless-stopped

networks:
  app-prod-network:
    driver: bridge
