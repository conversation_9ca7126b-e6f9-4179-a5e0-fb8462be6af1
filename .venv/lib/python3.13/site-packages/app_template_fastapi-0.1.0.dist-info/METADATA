Metadata-Version: 2.4
Name: app-template-fastapi
Version: 0.1.0
Summary: FastAPI application template with Docker support and development tools
Author-email: Your Name <<EMAIL>>
Keywords: api,docker,fastapi,template
Classifier: Development Status :: 4 - Beta
Classifier: Framework :: FastAPI
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.12
Requires-Dist: fastapi>=0.104.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: uvicorn[standard]>=0.24.0
Provides-Extra: dev
Requires-Dist: coverage>=7.3.0; extra == 'dev'
Requires-Dist: httpx>=0.25.0; extra == 'dev'
Requires-Dist: mypy>=1.7.0; extra == 'dev'
Requires-Dist: pre-commit>=3.5.0; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'dev'
Requires-Dist: pytest>=7.4.0; extra == 'dev'
Requires-Dist: ruff>=0.1.0; extra == 'dev'
Description-Content-Type: text/markdown

# FastAPI Application Template

A comprehensive FastAPI application template with Docker support, development tools, and production-ready configuration using [uv](https://docs.astral.sh/uv/) for dependency management.

## Features

- ⚡ **FastAPI** - Modern, fast web framework for building APIs
- 🐍 **Python 3.12+** - Latest Python features and performance improvements
- 📦 **uv** - Ultra-fast Python package installer and resolver
- 🐳 **Docker** - Containerized development and production environments
- 🔧 **Development Tools** - Pre-commit hooks, linting, formatting, and testing
- 🏗️ **Project Structure** - Well-organized, scalable project layout
- 🔒 **Security** - Production-ready security configurations
- 📊 **Monitoring** - Health checks and logging
- 🧪 **Testing** - Comprehensive test suite with pytest

## Quick Start

### Prerequisites

- Python 3.12+
- [uv](https://docs.astral.sh/uv/getting-started/installation/) (recommended) or pip
- Docker and Docker Compose (for containerized development)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd app-template-fastapi
   ```

2. **Install dependencies:**
   ```bash
   # Using uv (recommended)
   uv sync --dev

   # Or using pip
   pip install -e ".[dev]"
   ```

3. **Set up environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Install pre-commit hooks:**
   ```bash
   uv run pre-commit install
   ```

### Running the Application

#### Local Development

```bash
# Using make
make up-dev
```

#### Docker Development

```bash
# Start all services
docker-compose up --build

# Or using make
make docker-dev
```

The application will be available at:
- API: http://localhost:8000
- Documentation: http://localhost:8000/docs
- Alternative docs: http://localhost:8000/redoc

## Project Structure

```
app-template-fastapi/
├── app/                          # Application package
│   ├── __init__.py
│   ├── main.py                   # FastAPI application factory
│   ├── config.py                 # Configuration settings
│   └── api/                      # API routes
│       └── v1/                   # API version 1
│           ├── __init__.py
│           ├── router.py         # Main API router
│           └── endpoints/        # API endpoints
│               ├── __init__.py
│               ├── users.py      # User endpoints
│               └── items.py      # Item endpoints
├── tests/                        # Test suite
│   ├── __init__.py
│   └── test_main.py             # Main application tests
├── docker-compose.yml           # Development Docker Compose
├── docker-compose.prod.yml      # Production Docker Compose
├── Dockerfile.dev               # Development Dockerfile
├── Dockerfile.prod              # Production Dockerfile
├── nginx.conf                   # Nginx configuration
├── pyproject.toml              # Project configuration and dependencies
├── .env.example                # Environment variables template
├── .env.prod.example           # Production environment template
├── .pre-commit-config.yaml     # Pre-commit hooks configuration
├── .gitignore                  # Git ignore rules
├── Makefile                    # Development commands
└── README.md                   # This file
```

## Development

### Available Commands

Use the Makefile for common development tasks:

```bash
make clean                # Clean up cache and temporary files
make down-dev             # Stop development environment
make down-prod            # Stop production environment
make help                 # Show this help message
make up-dev               # Start development environment with Docker Compose
make up-prod              # Start production environment with Docker Compose
```

### Code Quality

This project uses several tools to maintain code quality:

- **Ruff** - Fast Python linter and formatter
- **MyPy** - Static type checking
- **Pre-commit** - Git hooks for code quality
- **Pytest** - Testing framework

### Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
# Application
APP_NAME=FastAPI Template
DEBUG=true

# Server
HOST=0.0.0.0
PORT=8000

# Database (optional)
# DATABASE_URL=postgresql://user:password@localhost/dbname

# CORS
CORS_ORIGINS=["http://localhost:3000"]
```

## API Documentation

Once the application is running, you can access:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### Example API Endpoints

- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /api/v1/users/` - List users
- `POST /api/v1/users/` - Create user
- `GET /api/v1/items/` - List items
- `POST /api/v1/items/` - Create item

## Docker

### Development

```bash
# Build and run development environment
docker-compose up --build

# Run in background
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### Production

```bash
# Build production image
docker build -f Dockerfile.prod -t fastapi-template:prod .

# Run production environment
docker-compose -f docker-compose.prod.yml up --build

# With specific environment file
cp .env.prod.example .env.prod
# Edit .env.prod with production values
docker-compose -f docker-compose.prod.yml up
```

## Production Deployment

### Security Considerations

1. **Environment Variables**: Never commit `.env` files. Use secure secret management.
2. **Database**: Use strong passwords and restrict access.
3. **HTTPS**: Configure SSL/TLS certificates for production.
4. **CORS**: Restrict CORS origins to your actual domains.
5. **Dependencies**: Regularly update dependencies for security patches.

### Production Checklist

- [ ] Set `DEBUG=false` in production environment
- [ ] Configure secure `SECRET_KEY`
- [ ] Set up proper database with backups
- [ ] Configure HTTPS with SSL certificates
- [ ] Set up monitoring and logging
- [ ] Configure proper CORS origins
- [ ] Set up health checks and alerts
- [ ] Review and harden Docker security settings

## Customization

### Adding New Endpoints

1. Create a new file in `app/api/v1/endpoints/`
2. Define your routes using FastAPI decorators
3. Add the router to `app/api/v1/router.py`

Example:
```python
# app/api/v1/endpoints/my_endpoint.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/")
async def my_endpoint():
    return {"message": "Hello from my endpoint"}
```

```python
# app/api/v1/router.py
from app.api.v1.endpoints import my_endpoint

api_router.include_router(
    my_endpoint.router,
    prefix="/my-endpoint",
    tags=["my-endpoint"]
)
```

### Adding Database Support

1. Add database dependencies to `pyproject.toml`:
   ```toml
   dependencies = [
       # ... existing dependencies
       "sqlalchemy>=2.0.0",
       "alembic>=1.12.0",
       "asyncpg>=0.29.0",  # for PostgreSQL
   ]
   ```

2. Update configuration in `app/config.py`
3. Create database models and connection logic
4. Add database initialization to `app/main.py`

### Adding Authentication

Consider adding:
- JWT token authentication
- OAuth2 integration
- User management system
- Role-based access control

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Run tests: `make test`
5. Run linting: `make lint`
6. Commit your changes: `git commit -am 'Add feature'`
7. Push to the branch: `git push origin feature-name`
8. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

- 📖 [FastAPI Documentation](https://fastapi.tiangolo.com/)
- 📦 [uv Documentation](https://docs.astral.sh/uv/)
- 🐳 [Docker Documentation](https://docs.docker.com/)
- 🐍 [Python Documentation](https://docs.python.org/3/)

## Changelog

### v0.1.0
- Initial FastAPI application template
- Docker development and production configurations
- Development tools setup (linting, formatting, testing)
- Basic API endpoints with CRUD operations
- Comprehensive documentation