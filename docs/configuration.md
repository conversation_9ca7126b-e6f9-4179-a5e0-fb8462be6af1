# Configuration Guide

This FastAPI application uses **pydantic-settings** for configuration management, providing a robust and type-safe way to handle application settings from multiple sources.

## Overview

The configuration system supports loading settings from:
- Default values defined in the code
- Environment variables
- `.env` files
- Command-line arguments (when using CLI features)

## Configuration Sources Priority

Settings are loaded in the following order (highest to lowest priority):
1. Environment variables
2. `.env` file
3. Default values

## Available Settings

### Application Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `APP_NAME` | str | "FastAPI Template" | Application name |
| `APP_VERSION` | str | "0.1.0" | Application version |
| `DEBUG` | bool | false | Debug mode |

### Server Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `HOST` | str | "0.0.0.0" | Server host |
| `PORT` | int | 8000 | Server port (1-65535) |
| `RELOAD` | bool | false | Auto-reload on code changes |

### CORS Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `CORS_ORIGINS` | list[str] | ["http://localhost:3000", "http://localhost:8080"] | CORS allowed origins |

The `CORS_ORIGINS` setting supports multiple formats:
- JSON array: `["https://example.com", "https://api.example.com"]`
- Comma-separated: `https://example.com,https://api.example.com`

### API Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `API_V1_PREFIX` | str | "/api/v1" | API v1 prefix |

### Security Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `SECRET_KEY` | str | "dev-secret-key-change-in-production" | Secret key for JWT tokens |
| `ACCESS_TOKEN_EXPIRE_MINUTES` | int | 30 | Access token expiration time |

⚠️ **Important**: Change the `SECRET_KEY` in production! The application will warn you if using default values.

### Database Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `POSTGRES_DB` | str | "fastapi_db" | PostgreSQL database name |
| `POSTGRES_USER` | str | "fastapi_user" | PostgreSQL username |
| `POSTGRES_PASSWORD` | str | "secure_password_change_this" | PostgreSQL password |

The `database_url` property automatically constructs the full PostgreSQL connection URL.

### Logging Settings

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `LOG_LEVEL` | str | "info" | Logging level (debug, info, warning, error, critical) |

## Usage Examples

### Using Environment Variables

```bash
export APP_NAME="My FastAPI App"
export DEBUG=true
export PORT=9000
export CORS_ORIGINS='["https://myapp.com", "https://api.myapp.com"]'
```

### Using .env File

Create a `.env` file in the project root:

```env
APP_NAME=My FastAPI App
DEBUG=true
PORT=9000
CORS_ORIGINS=["https://myapp.com", "https://api.myapp.com"]
SECRET_KEY=your-super-secret-key-here
POSTGRES_PASSWORD=your-secure-password
```

### Accessing Settings in Code

```python
from app.config import settings

# Access individual settings
print(f"App running on {settings.host}:{settings.port}")
print(f"Debug mode: {settings.debug}")
print(f"CORS origins: {settings.cors_origins}")

# Use computed properties
print(f"Database URL: {settings.database_url}")
```

### Creating Custom Settings Instance

```python
from app.config import Settings

# Load from specific env file
settings = Settings(_env_file="production.env")

# Load without env file (only defaults and env vars)
settings = Settings(_env_file=None)

# Override specific values
settings = Settings(debug=True, port=8080)
```

## Validation Features

The configuration system includes several validation features:

### Port Validation
- Must be between 1 and 65535

### Secret Key Validation
- Minimum length of 32 characters
- Warns if using default values

### Log Level Validation
- Must be one of: debug, info, warning, error, critical

### CORS Origins Parsing
- Automatically parses JSON arrays
- Supports comma-separated strings
- Validates format

## Environment-Specific Configuration

### Development
```env
DEBUG=true
LOG_LEVEL=debug
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]
```

### Production
```env
DEBUG=false
LOG_LEVEL=info
SECRET_KEY=your-production-secret-key
CORS_ORIGINS=["https://yourdomain.com"]
POSTGRES_PASSWORD=secure-production-password
```

## Best Practices

1. **Never commit secrets**: Use environment variables or secure secret management
2. **Use type hints**: The configuration is fully typed for better IDE support
3. **Validate early**: Settings are validated at application startup
4. **Environment isolation**: Use different `.env` files for different environments
5. **Document changes**: Update this guide when adding new settings

## Testing Configuration

The configuration system includes comprehensive tests. Run them with:

```bash
uv run python -m pytest tests/test_config.py -v
```

## Troubleshooting

### Common Issues

1. **Validation Error**: Check that environment variable types match expected types
2. **CORS Origins Not Working**: Ensure proper JSON format or comma separation
3. **Secret Key Warning**: Change the default secret key for production
4. **Port Already in Use**: Change the PORT setting to an available port

### Debug Configuration

To see all loaded settings:

```python
from app.config import settings
import json

# Print all settings (be careful with secrets!)
print(json.dumps(settings.model_dump(), indent=2, default=str))
```
